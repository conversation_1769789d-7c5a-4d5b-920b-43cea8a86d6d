/**
 * Unified Workspace Type Definitions
 * Consolidates all workspace-related types to eliminate duplication and inconsistencies
 */

import type { WorkspaceSettings as ApiWorkspaceSettings } from '@/services/api-types';

// Base workspace interface - core properties that all workspace representations share
export interface WorkspaceBase {
  id: string;
  name: string;
  description: string;
  isActive?: boolean;
}

// Extended workspace interface with additional metadata
export interface Workspace extends WorkspaceBase {
  members: number;
  contracts?: number;
  createdBy: string;
  createdDate: string;
  settings?: WorkspaceSettings;
}

// Workspace settings configuration
export interface WorkspaceSettings {
  autoAddUsers?: boolean;
  defaultRole?: string;
  visibility?: 'private' | 'public';
  features?: WorkspaceFeatures;
}

// Workspace feature flags
export interface WorkspaceFeatures {
  aiAnalysis?: boolean;
  advancedReporting?: boolean;
  customRoles?: boolean;
  apiAccess?: boolean;
}

// API response types for backend integration
export interface WorkspaceApiResponse {
  id: string;
  name: string;
  description: string | null;
  created_by: string;
  created_at: string;
  updated_at: string | null;
  members: number | null;
  contracts: number | null;
  is_active: boolean;
  settings: ApiWorkspaceSettings | null;
}

// Workspace creation payload
export interface WorkspaceCreatePayload {
  name: string;
  description?: string;
  settings?: WorkspaceSettings;
}

// Workspace update payload
export interface WorkspaceUpdatePayload {
  name?: string;
  description?: string;
  settings?: WorkspaceSettings;
  isActive?: boolean;
}

// Workspace member information
export interface WorkspaceMember {
  id: string;
  userId: string;
  workspaceId: string;
  roleId: string;
  joinedAt: string;
  status: 'active' | 'pending' | 'suspended';
  user?: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
}

// Workspace role definition
export interface WorkspaceRole {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  isSystem: boolean;
  userCount: number;
}

// Workspace permission definition
export interface WorkspacePermission {
  id: string;
  name: string;
  description: string;
  category: string;
}

// Workspace switching state
export interface WorkspaceSwitchState {
  isLoading: boolean;
  previousWorkspaceId: string | null;
  error: string | null;
  optimisticWorkspaceId: string | null;
}

// Workspace cache entry
export interface WorkspaceCacheEntry {
  workspace: Workspace;
  lastFetched: number;
  contracts?: Array<{
    id: string;
    title: string;
    type: string;
    status: string;
    created_at: string;
  }>;
  templates?: Array<{
    id: string;
    title: string;
    type: string;
    created_at: string;
  }>;
  members?: WorkspaceMember[];
}

// Workspace list filter options
export interface WorkspaceListFilters {
  search?: string;
  status?: 'active' | 'inactive' | 'all';
  sortBy?: 'name' | 'created' | 'members';
  sortOrder?: 'asc' | 'desc';
}

// Workspace analytics data
export interface WorkspaceAnalytics {
  totalContracts: number;
  activeMembers: number;
  recentActivity: number;
  storageUsed: number;
  lastActivity: string;
}

// Type guards for runtime type checking
export const isWorkspace = (obj: unknown): obj is Workspace => {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    'id' in obj &&
    'name' in obj &&
    'description' in obj &&
    'members' in obj &&
    'createdBy' in obj &&
    'createdDate' in obj &&
    typeof (obj as any).id === 'string' &&
    typeof (obj as any).name === 'string' &&
    typeof (obj as any).description === 'string' &&
    typeof (obj as any).members === 'number' &&
    typeof (obj as any).createdBy === 'string' &&
    typeof (obj as any).createdDate === 'string'
  );
};

export const isWorkspaceApiResponse = (obj: unknown): obj is WorkspaceApiResponse => {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    'id' in obj &&
    'name' in obj &&
    'created_by' in obj &&
    'created_at' in obj &&
    'is_active' in obj &&
    typeof (obj as any).id === 'string' &&
    typeof (obj as any).name === 'string' &&
    typeof (obj as any).created_by === 'string' &&
    typeof (obj as any).created_at === 'string' &&
    typeof (obj as any).is_active === 'boolean'
  );
};

// Workspace data transformation utilities
export const mapApiResponseToWorkspace = (apiResponse: WorkspaceApiResponse): Workspace => {
  return {
    id: apiResponse.id,
    name: apiResponse.name,
    description: apiResponse.description || '',
    members: apiResponse.members || 0,
    contracts: apiResponse.contracts || 0,
    createdBy: apiResponse.created_by,
    createdDate: apiResponse.created_at 
      ? new Date(apiResponse.created_at).toISOString().split('T')[0]
      : new Date().toISOString().split('T')[0],
    isActive: apiResponse.is_active,
    settings: apiResponse.settings || {},
  };
};

export const mapWorkspaceToCreatePayload = (workspace: Omit<Workspace, 'id' | 'members' | 'createdBy' | 'createdDate'>): WorkspaceCreatePayload => {
  return {
    name: workspace.name,
    description: workspace.description,
    settings: workspace.settings,
  };
};

export const mapWorkspaceToUpdatePayload = (workspace: Partial<Workspace>): WorkspaceUpdatePayload => {
  const payload: WorkspaceUpdatePayload = {};
  
  if (workspace.name !== undefined) payload.name = workspace.name;
  if (workspace.description !== undefined) payload.description = workspace.description;
  if (workspace.settings !== undefined) payload.settings = workspace.settings;
  if (workspace.isActive !== undefined) payload.isActive = workspace.isActive;
  
  return payload;
};

// Default workspace settings
export const DEFAULT_WORKSPACE_SETTINGS: WorkspaceSettings = {
  autoAddUsers: false,
  visibility: 'private' as const,
  defaultRole: 'viewer',
  features: {
    aiAnalysis: true,
    advancedReporting: false,
    customRoles: false,
    apiAccess: false,
  },
};

// Workspace validation utilities
export const validateWorkspaceName = (name: string): string | null => {
  if (!name || name.trim().length === 0) {
    return 'Workspace name is required';
  }
  if (name.length < 2) {
    return 'Workspace name must be at least 2 characters';
  }
  if (name.length > 50) {
    return 'Workspace name must be less than 50 characters';
  }
  if (!/^[a-zA-Z0-9\s\-_]+$/.test(name)) {
    return 'Workspace name can only contain letters, numbers, spaces, hyphens, and underscores';
  }
  return null;
};

export const validateWorkspaceDescription = (description: string): string | null => {
  if (description && description.length > 500) {
    return 'Workspace description must be less than 500 characters';
  }
  return null;
};
