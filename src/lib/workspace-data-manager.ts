import { WorkspaceService as APIWorkspaceService, ContractService, TemplateService } from './api-client';
import { Workspace } from './workspace-provider';

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const MAX_CACHE_SIZE = 10; // Maximum number of workspaces to cache

// Cache entry interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  isLoading: boolean;
  error: string | null;
}

// Workspace data cache
interface WorkspaceDataCache {
  contracts: CacheEntry<any[]>;
  templates: CacheEntry<any[]>;
  lastAccessed: number;
}

/**
 * Unified Workspace Data Manager
 *
 * Consolidates all workspace management functionality into a single layer:
 * - Workspace CRUD operations
 * - Intelligent caching with LRU eviction
 * - Request deduplication and cancellation
 * - Error handling and state management
 * - Integration with Clerk's organization system
 */
export class WorkspaceDataManager {
  private static instance: WorkspaceDataManager;

  // Core workspace data
  private workspaces: Workspace[] = [];
  private currentWorkspace: Workspace | null = null;
  private lastFetchTime: number | null = null;

  // Cache for workspace-specific data (contracts, templates)
  private dataCache = new Map<string, WorkspaceDataCache>();
  private accessOrder: string[] = [];

  // Request management
  private activeRequests = new Set<string>();
  private abortControllers = new Map<string, AbortController>();

  // State management
  private isLoading = false;
  private isRefreshing = false;
  private error: string | null = null;

  // Clerk integration
  private clerkContext: any = null;

  private constructor() {}

  static getInstance(): WorkspaceDataManager {
    if (!WorkspaceDataManager.instance) {
      WorkspaceDataManager.instance = new WorkspaceDataManager();
    }
    return WorkspaceDataManager.instance;
  }

  /**
   * Initialize with Clerk context for enhanced functionality
   */
  initialize(clerkContext: any): void {
    this.clerkContext = clerkContext;

    // If Clerk provides workspaces directly, use them
    if (clerkContext?.userWorkspaces) {
      this.workspaces = clerkContext.userWorkspaces;
      this.currentWorkspace = clerkContext.currentWorkspace;
      this.lastFetchTime = Date.now();
    }
  }

  /**
   * Get current state snapshot
   */
  getState() {
    return {
      workspaces: this.workspaces,
      currentWorkspace: this.currentWorkspace,
      isLoading: this.isLoading,
      isRefreshing: this.isRefreshing,
      error: this.error,
      lastFetchTime: this.lastFetchTime
    };
  }

  /**
   * Check if we should fetch workspaces
   */
  private shouldFetch(): boolean {
    // Don't fetch if already loading
    if (this.isLoading || this.activeRequests.size > 0) {
      return false;
    }

    // Don't fetch if we have recent data
    if (this.lastFetchTime && Date.now() - this.lastFetchTime < CACHE_DURATION) {
      return false;
    }

    return true;
  }

  /**
   * Cancel a specific request
   */
  private cancelRequest(requestId: string): void {
    const controller = this.abortControllers.get(requestId);
    if (controller) {
      controller.abort();
      this.abortControllers.delete(requestId);
      this.activeRequests.delete(requestId);
    }
  }

  /**
   * Cancel all active requests
   */
  private cancelAllRequests(): void {
    this.abortControllers.forEach((controller) => {
      controller.abort();
    });
    this.abortControllers.clear();
    this.activeRequests.clear();
  }

  /**
   * Fetch workspaces with deduplication and caching
   */
  async fetchWorkspaces(getToken: () => Promise<string | null>, force = false): Promise<Workspace[]> {
    const requestId = 'fetch-workspaces';

    // Check if we should fetch
    if (!force && !this.shouldFetch()) {
      return this.workspaces;
    }

    // Check if request is already active
    if (this.activeRequests.has(requestId)) {
      // Wait for the active request to complete
      while (this.activeRequests.has(requestId)) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.workspaces;
    }

    // Cancel any previous request
    this.cancelRequest(requestId);

    // Create new abort controller
    const abortController = new AbortController();
    this.abortControllers.set(requestId, abortController);
    this.activeRequests.add(requestId);

    // Set loading state
    if (!force) {
      this.isLoading = true;
    } else {
      this.isRefreshing = true;
    }
    this.error = null;

    try {
      // Get auth token
      const token = await getToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      // Make API call
      const response = await APIWorkspaceService.getWorkspaces(token);

      // Check if request was cancelled
      if (abortController.signal.aborted) {
        throw new Error('Request was cancelled');
      }

      if (!response.data) {
        throw new Error('No workspace data received from backend');
      }

      // Map backend workspace data to frontend format
      const workspaces: Workspace[] = response.data.map((workspace: any) => ({
        id: workspace.id,
        name: workspace.name,
        description: workspace.description || '',
        members: workspace.members || 0,
        contracts: workspace.contracts || 0,
        createdBy: workspace.created_by || "Unknown",
        createdDate: workspace.created_at
          ? new Date(workspace.created_at).toISOString().split('T')[0]
          : new Date().toISOString().split('T')[0],
        isActive: false, // Will be set by the workspace provider
      }));

      // Update state
      this.workspaces = workspaces;
      this.lastFetchTime = Date.now();
      this.error = null;

      return workspaces;
    } catch (error: any) {
      if (error.name === 'AbortError') {
        throw error;
      }

      this.error = error.message || 'Failed to fetch workspaces';
      throw error;
    } finally {
      // Cleanup
      this.activeRequests.delete(requestId);
      this.abortControllers.delete(requestId);
      this.isLoading = false;
      this.isRefreshing = false;
    }
  }

  /**
   * Switch to a workspace
   */
  async switchWorkspace(workspaceId: string): Promise<void> {
    try {
      // If using Clerk, delegate to Clerk's switching
      if (this.clerkContext?.switchToWorkspace) {
        await this.clerkContext.switchToWorkspace(workspaceId);
        this.currentWorkspace = this.clerkContext.currentWorkspace;
        return;
      }

      // Otherwise, find and set the workspace
      const workspace = this.workspaces.find(ws => ws.id === workspaceId);
      if (!workspace) {
        throw new Error(`Workspace with ID ${workspaceId} not found`);
      }

      this.currentWorkspace = workspace;
    } catch (error) {
      console.error('Failed to switch workspace:', error);
      throw error;
    }
  }

  /**
   * Create a new workspace
   */
  async createWorkspace(workspaceData: Omit<Workspace, "id">): Promise<Workspace> {
    try {
      // If using Clerk, delegate to Clerk's creation
      if (this.clerkContext?.createWorkspace) {
        const newWorkspace = await this.clerkContext.createWorkspace(workspaceData);
        this.workspaces = [...this.workspaces, newWorkspace];
        this.currentWorkspace = newWorkspace;
        return newWorkspace;
      }

      // Otherwise, create via API
      throw new Error('Direct workspace creation not implemented without Clerk');
    } catch (error) {
      console.error('Failed to create workspace:', error);
      throw error;
    }
  }

  /**
   * Update workspace
   */
  async updateWorkspace(id: string, workspaceData: Partial<Workspace>): Promise<Workspace | null> {
    try {
      // If using Clerk, delegate to Clerk's update
      if (this.clerkContext?.updateWorkspace) {
        const updatedWorkspace = await this.clerkContext.updateWorkspace(id, workspaceData);

        if (updatedWorkspace) {
          // Update local state
          this.workspaces = this.workspaces.map(ws =>
            ws.id === id ? updatedWorkspace : ws
          );

          // Update current workspace if it's the one being updated
          if (this.currentWorkspace?.id === id) {
            this.currentWorkspace = updatedWorkspace;
          }
        }

        return updatedWorkspace;
      }

      // Otherwise, update via API
      throw new Error('Direct workspace update not implemented without Clerk');
    } catch (error) {
      console.error('Failed to update workspace:', error);
      throw error;
    }
  }

  /**
   * Delete workspace
   */
  async deleteWorkspace(id: string): Promise<boolean> {
    try {
      // If using Clerk, delegate to Clerk's deletion
      if (this.clerkContext?.deleteWorkspace) {
        const success = await this.clerkContext.deleteWorkspace(id);

        if (success) {
          // Update local state
          this.workspaces = this.workspaces.filter(ws => ws.id !== id);

          // Clear current workspace if it was deleted
          if (this.currentWorkspace?.id === id) {
            this.currentWorkspace = this.workspaces.length > 0 ? this.workspaces[0] : null;
          }
        }

        return success;
      }

      // Otherwise, delete via API
      throw new Error('Direct workspace deletion not implemented without Clerk');
    } catch (error) {
      console.error('Failed to delete workspace:', error);
      throw error;
    }
  }

  /**
   * Refresh workspaces from backend
   */
  async refreshWorkspaces(getToken?: () => Promise<string | null>): Promise<Workspace[]> {
    try {
      // If using Clerk, delegate to Clerk's refresh
      if (this.clerkContext?.fetchUserWorkspaces) {
        const workspaces = await this.clerkContext.fetchUserWorkspaces();
        this.workspaces = workspaces;
        this.lastFetchTime = Date.now();
        return workspaces;
      }

      // Otherwise, fetch via API
      if (getToken) {
        return this.fetchWorkspaces(getToken, true);
      }

      throw new Error('No token provider available for workspace refresh');
    } catch (error) {
      console.error('Failed to refresh workspaces:', error);
      this.error = error instanceof Error ? error.message : 'Failed to refresh workspaces';
      throw error;
    }
  }

  // ===== WORKSPACE ACCESS CONTROL =====

  /**
   * Check if user can access a workspace
   */
  canAccessWorkspace(workspaceId: string): boolean {
    if (this.clerkContext?.canAccessWorkspace) {
      return this.clerkContext.canAccessWorkspace(workspaceId);
    }

    // Fallback: check if workspace exists in user's workspaces
    return this.workspaces.some(ws => ws.id === workspaceId);
  }

  /**
   * Check if user can access content from a workspace
   */
  canAccessContent(workspaceId: string | undefined): boolean {
    if (!workspaceId) return false;

    if (this.clerkContext?.canAccessContent) {
      return this.clerkContext.canAccessContent(workspaceId);
    }

    return this.canAccessWorkspace(workspaceId);
  }

  /**
   * Validate workspace access (throws error if no access)
   */
  validateWorkspaceAccess(workspaceId: string): void {
    if (!this.canAccessWorkspace(workspaceId)) {
      throw new Error(`Access denied: You don't have permission to access workspace ${workspaceId}`);
    }
  }

  /**
   * Get workspace by ID
   */
  getWorkspaceById(id: string): Workspace | undefined {
    if (this.clerkContext?.getWorkspaceById) {
      return this.clerkContext.getWorkspaceById(id);
    }

    return this.workspaces.find(ws => ws.id === id);
  }

  /**
   * Check if user is in a specific workspace
   */
  isUserInWorkspace(workspaceId: string): boolean {
    return this.canAccessWorkspace(workspaceId);
  }

  /**
   * Check if content belongs to current workspace
   */
  isContentInWorkspace(workspaceId: string | undefined): boolean {
    if (!workspaceId || !this.currentWorkspace) return false;
    return workspaceId === this.currentWorkspace.id;
  }

  /**
   * Get current user
   */
  getCurrentUser() {
    return this.clerkContext?.currentUser || null;
  }

  /**
   * Get current workspace
   */
  getCurrentWorkspace(): Workspace | null {
    return this.currentWorkspace;
  }

  /**
   * Get all user workspaces
   */
  getUserWorkspaces(): Workspace[] {
    return this.workspaces;
  }

  /**
   * Check if data is loading
   */
  getIsLoading(): boolean {
    return this.isLoading;
  }

  /**
   * Check if data is refreshing
   */
  getIsRefreshing(): boolean {
    return this.isRefreshing;
  }

  /**
   * Get current error
   */
  getError(): string | null {
    return this.error;
  }

  /**
   * Clear error state
   */
  clearError(): void {
    this.error = null;
  }

  // ===== WORKSPACE DATA CACHING =====

  /**
   * Initialize cache entry for a workspace
   */
  private initializeCacheEntry<T>(data: T[] = []): CacheEntry<T[]> {
    return {
      data,
      timestamp: Date.now(),
      isLoading: false,
      error: null,
    };
  }

  /**
   * Get or create cache for workspace
   */
  private getWorkspaceCache(workspaceId: string): WorkspaceDataCache {
    if (!this.dataCache.has(workspaceId)) {
      this.dataCache.set(workspaceId, {
        contracts: this.initializeCacheEntry(),
        templates: this.initializeCacheEntry(),
        lastAccessed: Date.now(),
      });
    }

    // Update access order for LRU eviction
    this.updateAccessOrder(workspaceId);
    return this.dataCache.get(workspaceId)!;
  }

  /**
   * Update access order for LRU cache
   */
  private updateAccessOrder(workspaceId: string): void {
    const index = this.accessOrder.indexOf(workspaceId);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
    this.accessOrder.push(workspaceId);

    // Evict least recently used entries if cache is full
    while (this.accessOrder.length > MAX_CACHE_SIZE) {
      const lruWorkspaceId = this.accessOrder.shift()!;
      this.dataCache.delete(lruWorkspaceId);
      this.abortControllers.get(lruWorkspaceId)?.abort();
      this.abortControllers.delete(lruWorkspaceId);
    }
  }

  /**
   * Check if cache entry is valid
   */
  private isCacheValid<T>(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp < CACHE_DURATION && !entry.error;
  }

  /**
   * Fetch contracts with caching
   */
  async getContracts(workspaceId: string, token?: string, force = false): Promise<any[]> {
    if (!workspaceId) {
      throw new Error('Workspace ID is required for fetching contracts');
    }

    this.validateWorkspaceAccess(workspaceId);
    const cache = this.getWorkspaceCache(workspaceId);

    // Return cached data if valid and not forcing refresh
    if (!force && this.isCacheValid(cache.contracts) && !cache.contracts.isLoading) {
      return cache.contracts.data;
    }

    // Return cached data immediately if loading (optimistic)
    if (cache.contracts.isLoading) {
      return cache.contracts.data;
    }

    // Start loading
    cache.contracts.isLoading = true;
    cache.contracts.error = null;

    try {
      const response = await ContractService.getContracts(
        { workspace_id: workspaceId },
        token
      );

      if (response.data) {
        cache.contracts.data = response.data;
        cache.contracts.timestamp = Date.now();
      }
    } catch (error: any) {
      if (error.status === 403 || error.message?.includes('Access denied')) {
        cache.contracts.error = 'Access denied: You don\'t have permission to access this workspace';
      } else {
        cache.contracts.error = error.message || 'Failed to fetch contracts';
      }
    } finally {
      cache.contracts.isLoading = false;
    }

    return cache.contracts.data;
  }

  /**
   * Fetch templates with caching
   */
  async getTemplates(workspaceId: string, force = false): Promise<any[]> {
    if (!workspaceId) {
      throw new Error('Workspace ID is required for fetching templates');
    }

    this.validateWorkspaceAccess(workspaceId);
    const cache = this.getWorkspaceCache(workspaceId);

    // Return cached data if valid and not forcing refresh
    if (!force && this.isCacheValid(cache.templates) && !cache.templates.isLoading) {
      return cache.templates.data;
    }

    // Return cached data immediately if loading (optimistic)
    if (cache.templates.isLoading) {
      return cache.templates.data;
    }

    // Start loading
    cache.templates.isLoading = true;
    cache.templates.error = null;

    try {
      const response = await TemplateService.getTemplates({
        workspace_id: workspaceId,
      });

      if (response.data) {
        cache.templates.data = response.data;
        cache.templates.timestamp = Date.now();
      }
    } catch (error: any) {
      if (error.status === 403 || error.message?.includes('Access denied')) {
        cache.templates.error = 'Access denied: You don\'t have permission to access this workspace';
      } else {
        cache.templates.error = error.message || 'Failed to fetch templates';
      }
    } finally {
      cache.templates.isLoading = false;
    }

    return cache.templates.data;
  }

  /**
   * Clear cache for specific workspace
   */
  clearWorkspaceCache(workspaceId: string): void {
    this.dataCache.delete(workspaceId);
    const index = this.accessOrder.indexOf(workspaceId);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  /**
   * Clear all cache
   */
  clearAllCache(): void {
    this.dataCache.clear();
    this.accessOrder = [];
    this.cancelAllRequests();
  }

  // ===== ROLE AND PERMISSION MANAGEMENT =====

  /**
   * Role and permission management methods (delegate to Clerk if available)
   */
  async getWorkspaceRoles(workspaceId: string) {
    if (this.clerkContext?.getWorkspaceRoles) {
      return this.clerkContext.getWorkspaceRoles(workspaceId);
    }
    throw new Error('Role management not available without Clerk context');
  }

  async getWorkspacePermissions(workspaceId: string) {
    if (this.clerkContext?.getWorkspacePermissions) {
      return this.clerkContext.getWorkspacePermissions(workspaceId);
    }
    throw new Error('Permission management not available without Clerk context');
  }

  async hasPermission(workspaceId: string, userId: string, permissionId: string) {
    if (this.clerkContext?.hasPermission) {
      return this.clerkContext.hasPermission(workspaceId, userId, permissionId);
    }
    return false; // Default to no permission
  }

  async hasAnyPermission(workspaceId: string, userId: string, permissionIds: string[]) {
    if (this.clerkContext?.hasAnyPermission) {
      return this.clerkContext.hasAnyPermission(workspaceId, userId, permissionIds);
    }
    return false; // Default to no permission
  }
}

// Export singleton instance
export const workspaceDataManager = WorkspaceDataManager.getInstance();
