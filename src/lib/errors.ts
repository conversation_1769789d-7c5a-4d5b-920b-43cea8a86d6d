/**
 * Unified Error Handling System for Averum Contracts
 * 
 * This module provides custom error classes and error handling utilities
 * to improve error reporting and user experience throughout the application.
 */

// Base error class for all application errors
export abstract class AppError extends Error {
  abstract readonly code: string;
  abstract readonly statusCode: number;
  readonly timestamp: Date;
  readonly details?: Record<string, any>;

  constructor(message: string, details?: Record<string, any>) {
    super(message);
    this.name = this.constructor.name;
    this.timestamp = new Date();
    this.details = details;
    
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      statusCode: this.statusCode,
      timestamp: this.timestamp.toISOString(),
      details: this.details,
      stack: this.stack
    };
  }
}

// API-related errors
export class APIError extends AppError {
  readonly code = 'API_ERROR';
  readonly statusCode: number;

  constructor(message: string, statusCode: number = 500, details?: Record<string, any>) {
    super(message, details);
    this.statusCode = statusCode;
  }
}

export class NetworkError extends AppError {
  readonly code = 'NETWORK_ERROR';
  readonly statusCode = 503;

  constructor(message: string = 'Network connection failed', details?: Record<string, any>) {
    super(message, details);
  }
}

export class AuthenticationError extends AppError {
  readonly code = 'AUTH_ERROR';
  readonly statusCode = 401;

  constructor(message: string = 'Authentication failed', details?: Record<string, any>) {
    super(message, details);
  }
}

export class AuthorizationError extends AppError {
  readonly code = 'AUTHORIZATION_ERROR';
  readonly statusCode = 403;

  constructor(message: string = 'Access denied', details?: Record<string, any>) {
    super(message, details);
  }
}

// Workspace-related errors
export class WorkspaceError extends AppError {
  readonly code = 'WORKSPACE_ERROR';
  readonly statusCode = 400;

  constructor(message: string, details?: Record<string, any>) {
    super(message, details);
  }
}

export class WorkspaceNotFoundError extends WorkspaceError {
  readonly notFoundCode = 'WORKSPACE_NOT_FOUND';
  readonly notFoundStatusCode = 404;

  constructor(workspaceId?: string) {
    super(
      workspaceId 
        ? `Workspace with ID "${workspaceId}" not found`
        : 'Workspace not found',
      { workspaceId }
    );
  }
}

// Contract-related errors
export class ContractError extends AppError {
  readonly code = 'CONTRACT_ERROR';
  readonly statusCode = 400;

  constructor(message: string, details?: Record<string, any>) {
    super(message, details);
  }
}

export class ContractNotFoundError extends ContractError {
  readonly notFoundCode = 'CONTRACT_NOT_FOUND';
  readonly notFoundStatusCode = 404;

  constructor(contractId?: string) {
    super(
      contractId 
        ? `Contract with ID "${contractId}" not found`
        : 'Contract not found',
      { contractId }
    );
  }
}

export class ContractValidationError extends ContractError {
  readonly validationCode = 'CONTRACT_VALIDATION_ERROR';
  readonly validationStatusCode = 422;

  constructor(message: string, validationErrors?: Record<string, string[]>) {
    super(message, { validationErrors });
  }
}

// Document processing errors
export class DocumentError extends AppError {
  readonly code = 'DOCUMENT_ERROR';
  readonly statusCode = 400;

  constructor(message: string, details?: Record<string, any>) {
    super(message, details);
  }
}

export class DocumentParsingError extends DocumentError {
  readonly parsingCode = 'DOCUMENT_PARSING_ERROR';
  readonly parsingStatusCode = 422;

  constructor(fileName?: string, fileType?: string) {
    super(
      fileName 
        ? `Failed to parse document "${fileName}"`
        : 'Failed to parse document',
      { fileName, fileType }
    );
  }
}

export class UnsupportedFileTypeError extends DocumentError {
  readonly unsupportedCode = 'UNSUPPORTED_FILE_TYPE';
  readonly unsupportedStatusCode = 415;

  constructor(fileType: string, supportedTypes: string[] = []) {
    super(
      `File type "${fileType}" is not supported`,
      { fileType, supportedTypes }
    );
  }
}

// AI service errors
export class AIError extends AppError {
  readonly code = 'AI_ERROR';
  readonly statusCode = 500;

  constructor(message: string, details?: Record<string, any>) {
    super(message, details);
  }
}

export class AIAnalysisError extends AIError {
  readonly analysisCode = 'AI_ANALYSIS_ERROR';
  readonly analysisStatusCode = 500;

  constructor(message: string = 'AI analysis failed', details?: Record<string, any>) {
    super(message, details);
  }
}

export class AIServiceUnavailableError extends AIError {
  readonly unavailableCode = 'AI_SERVICE_UNAVAILABLE';
  readonly unavailableStatusCode = 503;

  constructor(message: string = 'AI service is temporarily unavailable') {
    super(message);
  }
}

// Validation errors
export class ValidationError extends AppError {
  readonly code = 'VALIDATION_ERROR';
  readonly statusCode = 422;

  constructor(message: string, fieldErrors?: Record<string, string[]>) {
    super(message, { fieldErrors });
  }
}

// Rate limiting errors
export class RateLimitError extends AppError {
  readonly code = 'RATE_LIMIT_ERROR';
  readonly statusCode = 429;

  constructor(message: string = 'Too many requests', retryAfter?: number) {
    super(message, { retryAfter });
  }
}

// Error factory function
export function createErrorFromResponse(response: any, defaultMessage: string = 'An error occurred'): AppError {
  const status = response?.status || response?.statusCode || 500;
  const message = response?.message || response?.detail || defaultMessage;
  const details = response?.details || {};

  // Map status codes to specific error types
  switch (status) {
    case 401:
      return new AuthenticationError(message, details);
    case 403:
      return new AuthorizationError(message, details);
    case 404:
      return new APIError(message, status, details);
    case 422:
      return new ValidationError(message, details.fieldErrors || details.validationErrors);
    case 429:
      return new RateLimitError(message, details.retryAfter);
    case 503:
      return new NetworkError(message, details);
    default:
      return new APIError(message, status, details);
  }
}

// Error handler utility
export class ErrorHandler {
  static handle(error: unknown, context?: string): AppError {
    // If it's already an AppError, return it
    if (error instanceof AppError) {
      return error;
    }

    // If it's a standard Error
    if (error instanceof Error) {
      return new APIError(
        error.message || 'An unexpected error occurred',
        500,
        { originalError: error.name, context }
      );
    }

    // If it's an object with error information
    if (typeof error === 'object' && error !== null) {
      return createErrorFromResponse(error, 'An unexpected error occurred');
    }

    // Fallback for unknown error types
    return new APIError(
      'An unknown error occurred',
      500,
      { originalError: String(error), context }
    );
  }

  static async handleAsync<T>(
    operation: () => Promise<T>,
    context?: string
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      throw this.handle(error, context);
    }
  }

  static handleSync<T>(
    operation: () => T,
    context?: string
  ): T {
    try {
      return operation();
    } catch (error) {
      throw this.handle(error, context);
    }
  }
}

// User-friendly error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Please check your internet connection and try again.',
  AUTH_ERROR: 'Please sign in again to continue.',
  AUTHORIZATION_ERROR: 'You don\'t have permission to perform this action.',
  WORKSPACE_NOT_FOUND: 'The requested workspace could not be found.',
  CONTRACT_NOT_FOUND: 'The requested contract could not be found.',
  DOCUMENT_PARSING_ERROR: 'Unable to read the document. Please try a different file format.',
  UNSUPPORTED_FILE_TYPE: 'This file type is not supported. Please use PDF, DOCX, or TXT files.',
  AI_SERVICE_UNAVAILABLE: 'AI analysis is temporarily unavailable. Please try again later.',
  RATE_LIMIT_ERROR: 'Too many requests. Please wait a moment before trying again.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  GENERIC_ERROR: 'Something went wrong. Please try again later.'
} as const;

// Get user-friendly message for error
export function getUserFriendlyMessage(error: AppError): string {
  return ERROR_MESSAGES[error.code as keyof typeof ERROR_MESSAGES] || 
         error.message || 
         ERROR_MESSAGES.GENERIC_ERROR;
}
