import React, { createContext, useContext, useState, useEffect, useRef, useCallback } from "react";
import { useUser, useOrganization<PERSON>ist, use<PERSON><PERSON><PERSON>, useAuth } from "@clerk/clerk-react";
import { Workspace } from "../types/workspace";
import { User } from "./workspace-provider";
import { api } from "./api";
import {
  DEFAULT_ROLES,
  DEFAULT_PERMISSIONS,
  Role,
  Permission,
  getRoleIdFromMembership,
  initializeOrganizationRoles
} from "./clerk-roles";
import { useWorkspaces } from "../hooks/useWorkspaces";
import { centralizedWorkspaceService } from "../services/workspace-service";

interface ClerkWorkspaceContextType {
  // Core workspace state
  currentWorkspace: Workspace | null;
  setCurrentWorkspace: (workspace: Workspace) => void;
  currentUser: User | null;
  userWorkspaces: Workspace[];
  isLoading: boolean;

  // Workspace access validation
  isUserInWorkspace: (workspaceId: string) => boolean;
  isContentInWorkspace: (workspaceId: string | undefined) => boolean;
  canAccessWorkspace: (workspaceId: string) => boolean;
  canAccessContent: (workspaceId: string | undefined) => boolean;
  validateWorkspaceAccess: (workspaceId: string) => void;

  // Simplified workspace operations (using centralized service)
  createWorkspace: (workspace: Omit<Workspace, "id" | "members" | "createdBy" | "createdDate">) => Promise<Workspace>;
  updateWorkspace: (id: string, workspace: Partial<Workspace>) => Promise<Workspace | null>;
  deleteWorkspace: (id: string) => Promise<boolean>;
  getWorkspaceById: (id: string) => Workspace | undefined;
  getUserWorkspaces: () => Workspace[];
  switchToWorkspace: (workspaceId: string) => Promise<void>;

  // Role management (simplified)
  getWorkspaceRoles: (workspaceId: string) => Promise<Role[]>;
  getWorkspacePermissions: (workspaceId: string) => Promise<Permission[]>;
  getUserRole: (workspaceId: string, userId: string) => Promise<Role | null>;
  hasPermission: (workspaceId: string, userId: string, permissionId: string) => Promise<boolean>;
  hasAnyPermission: (workspaceId: string, userId: string, permissionIds: string[]) => Promise<boolean>;
}

const ClerkWorkspaceContext = createContext<ClerkWorkspaceContextType | undefined>(undefined);

export const ClerkWorkspaceProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Get user data from Clerk
  const { user, isLoaded: isUserLoaded } = useUser();
  const clerk = useClerk();
  const { getToken } = useAuth();

  // Get organization data from Clerk
  const { userMemberships } = useOrganizationList();

  // Local state
  const [currentWorkspace, setCurrentWorkspaceState] = useState<Workspace | null>(null);
  const [userWorkspaces, setUserWorkspaces] = useState<Workspace[]>([]);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Ref to track if we've set the initial workspace to prevent infinite loops
  const hasSetInitialWorkspace = useRef(false);

  // Convert Clerk user to our User type
  useEffect(() => {
    if (isUserLoaded && user) {
      const workspaceIds = userMemberships?.data?.map(membership => membership.organization.id) || [];

      // Only update if the user data has actually changed
      setCurrentUser(prevUser => {
        const newUser = {
          id: user.id,
          name: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username || 'User',
          email: user.primaryEmailAddress?.emailAddress || '',
          role: 'member', // Default role, can be updated based on organization membership
          initials: getInitials(`${user.firstName || ''} ${user.lastName || ''}`),
          workspaces: workspaceIds
        };

        // Only update if something actually changed
        if (!prevUser ||
            prevUser.id !== newUser.id ||
            prevUser.name !== newUser.name ||
            prevUser.email !== newUser.email ||
            JSON.stringify(prevUser.workspaces) !== JSON.stringify(newUser.workspaces)) {
          return newUser;
        }
        return prevUser;
      });
    }
  }, [isUserLoaded, user?.id, user?.firstName, user?.lastName, user?.username, user?.primaryEmailAddress?.emailAddress, userMemberships?.data?.length]);

  // Memoize callbacks to prevent infinite re-renders
  const onWorkspacesSuccess = useCallback((workspaces: Workspace[]) => {
    console.log("✅ ClerkWorkspaceProvider: Workspaces loaded successfully:", workspaces.length);
  }, []);

  const onWorkspacesError = useCallback((error: string) => {
    // Don't log cancelled requests as errors
    if (error.includes('cancelled') || error.includes('AbortError')) {
      console.log("🚫 ClerkWorkspaceProvider: Workspace request was cancelled");
      return;
    }
    console.error("❌ ClerkWorkspaceProvider: Error loading workspaces:", error);
  }, []);

  // Use the centralized workspace hook
  const {
    workspaces: fetchedWorkspaces,
    isLoading: workspaceLoading,
    setCurrentWorkspace: setStoreCurrentWorkspace,
  } = useWorkspaces({
    autoFetch: true,
    refetchOnAuth: true,
    onSuccess: onWorkspacesSuccess,
    onError: onWorkspacesError,
  });

  // Sync store state with local state
  useEffect(() => {
    if (fetchedWorkspaces.length > 0) {
      setUserWorkspaces(fetchedWorkspaces);
    }

    // Update loading state
    setIsLoading(workspaceLoading);
  }, [fetchedWorkspaces, workspaceLoading]);

  // Separate effect for setting initial workspace (only runs once when workspaces are first loaded)
  useEffect(() => {
    if (fetchedWorkspaces.length > 0 && !hasSetInitialWorkspace.current) {
      const activeWorkspace = fetchedWorkspaces.find(ws => ws.isActive) || fetchedWorkspaces[0];
      setCurrentWorkspaceState(activeWorkspace);
      setStoreCurrentWorkspace(activeWorkspace);
      api.setWorkspaceId(activeWorkspace.id);
      hasSetInitialWorkspace.current = true;
    }
  }, [fetchedWorkspaces, setStoreCurrentWorkspace]); // Safe dependencies - no infinite loop

  // Helper function to get initials from name
  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2) || 'U';
  };

  // Enhanced workspace switching function with optimistic updates
  const setCurrentWorkspace = async (workspace: Workspace) => {
    try {
      // Only allow setting workspaces the user has access to
      if (!currentUser || !currentUser.workspaces.includes(workspace.id)) {
        console.warn(`User does not have access to workspace: ${workspace.id}`);
        return;
      }

      console.log(`🔄 Switching to workspace: ${workspace.name} (${workspace.id})`);

      // Optimistic update - immediately update UI state
      const workspaceWithActiveFlag = { ...workspace, isActive: true };
      setCurrentWorkspaceState(workspaceWithActiveFlag);

      // Update userWorkspaces to reflect the new active workspace
      const updatedWorkspaces = userWorkspaces.map(ws => ({
        ...ws,
        isActive: ws.id === workspace.id
      }));
      setUserWorkspaces(updatedWorkspaces);

      // Set the workspace ID in the API service immediately
      api.setWorkspaceId(workspace.id);

      // Background operations (non-blocking)
      Promise.all([
        // Switch the active organization in Clerk
        clerk.setActive({ organization: workspace.id }).catch(error => {
          console.error("Failed to set active organization in Clerk:", error);
          // Non-critical error - workspace switch can continue
        }),

        // Refresh workspace data from backend
        (async () => {
          try {
            const token = await getToken();
            if (token) {
              const { WorkspaceService } = await import('../services/api-services');
              const response = await WorkspaceService.getWorkspace(workspace.id, false, token);
              if (response.data) {
                // Update the workspace with fresh data from backend
                const freshWorkspace = {
                  id: response.data.id,
                  name: response.data.name,
                  description: response.data.description || '',
                  members: response.data.members || 0,
                  contracts: response.data.contracts || 0,
                  createdBy: response.data.created_by || "Unknown",
                  createdDate: response.data.created_at ? new Date(response.data.created_at).toISOString().split('T')[0] : workspace.createdDate,
                  isActive: true,
                };
                setCurrentWorkspaceState(freshWorkspace);
              }
            }
          } catch (error) {
            console.warn("Failed to refresh workspace data from backend:", error);
            // Non-critical error - workspace switch has already succeeded
          }
        })()
      ]).then(() => {
        console.log(`✅ Successfully switched to workspace: ${workspace.name}`);
      });

    } catch (error) {
      console.error("❌ Failed to switch workspace:", error);
      throw error; // Re-throw for error handling in optimized hook
    }
  };

  // Check if user belongs to a workspace
  const isUserInWorkspace = (workspaceId: string): boolean => {
    return currentUser?.workspaces.includes(workspaceId) || false;
  };

  // Check if content belongs to the current workspace
  const isContentInWorkspace = (workspaceId: string | undefined): boolean => {
    if (!workspaceId || !currentWorkspace) return false;
    return workspaceId === currentWorkspace.id;
  };

  // CRUD operations for workspaces using centralized service
  const createWorkspace = async (workspaceData: Omit<Workspace, "id" | "members" | "createdBy" | "createdDate">): Promise<Workspace> => {
    let clerkOrganization: any = null;

    try {
      // Step 1: Create a new organization in Clerk
      const params = {
        name: workspaceData.name,
        publicMetadata: {
          description: workspaceData.description || '',
          roles: DEFAULT_ROLES,
          permissions: DEFAULT_PERMISSIONS,
          rolesInitialized: true
        }
      } as any;

      clerkOrganization = await clerk.createOrganization(params);

      if (!clerkOrganization) {
        throw new Error("Failed to create organization in Clerk");
      }

      // Step 2: Create workspace in database using centralized service
      try {
        const backendWorkspaceData = {
          name: workspaceData.name,
          description: workspaceData.description || '',
          settings: workspaceData.settings || {}
        };

        const newWorkspace = await centralizedWorkspaceService.createWorkspace(backendWorkspaceData, getToken);

        // Step 3: Set the new organization as active
        await clerk.setActive({ organization: clerkOrganization.id });

        // Step 4: Update local state
        const workspaceWithClerkId = {
          ...newWorkspace,
          id: clerkOrganization.id, // Use Clerk organization ID
          isActive: true,
          createdBy: currentUser?.name || "Unknown",
        };

        // Update API service
        api.setWorkspaceId(clerkOrganization.id);

        // Update local state
        setCurrentWorkspaceState(workspaceWithClerkId);
        const updatedWorkspaces = [
          ...userWorkspaces.map(ws => ({ ...ws, isActive: false })),
          workspaceWithClerkId
        ];
        setUserWorkspaces(updatedWorkspaces);

        // Update current user's workspaces
        if (currentUser) {
          setCurrentUser({
            ...currentUser,
            workspaces: [...currentUser.workspaces, clerkOrganization.id]
          });
        }

        return workspaceWithClerkId;

      } catch (backendError) {
        console.error("Failed to create workspace in database:", backendError);

        // Rollback: Delete the Clerk organization
        try {
          await clerkOrganization.destroy();
          console.log("Rolled back Clerk organization due to database error");
        } catch (rollbackError) {
          console.error("Failed to rollback Clerk organization:", rollbackError);
        }

        throw new Error(`Failed to create workspace: ${backendError}`);
      }

    } catch (error) {
      console.error("Error creating workspace:", error);
      throw error;
    }
  };

  const updateWorkspace = async (id: string, workspaceData: Partial<Workspace>): Promise<Workspace | null> => {
    try {
      // Find the organization in Clerk
      const membership = userMemberships?.data.find(m => m.organization.id === id);
      if (!membership) {
        return null;
      }

      // Update the organization in Clerk
      if (workspaceData.name) {
        await membership.organization.update({ name: workspaceData.name });
      }

      if (workspaceData.description) {
        // Update the description in the organization's metadata
        // Get current metadata
        const currentMetadata = membership.organization.publicMetadata || {};

        // Use type assertion to work around TypeScript definition issues
        const updateParams = {
          name: membership.organization.name, // Keep the current name
          publicMetadata: {
            ...currentMetadata,
            description: workspaceData.description
          }
        } as any;

        await membership.organization.update(updateParams);
      }

      // Find the workspace to update in local state
      const workspaceIndex = userWorkspaces.findIndex(ws => ws.id === id);
      if (workspaceIndex === -1) return null;

      // Create the updated workspace
      const updatedWorkspace = {
        ...userWorkspaces[workspaceIndex],
        ...workspaceData
      };

      // Update the workspace in the list
      const updatedWorkspaces = [...userWorkspaces];
      updatedWorkspaces[workspaceIndex] = updatedWorkspace;
      setUserWorkspaces(updatedWorkspaces);

      // If the current workspace is being updated, update it as well
      if (currentWorkspace && currentWorkspace.id === id) {
        setCurrentWorkspaceState(updatedWorkspace);
      }

      return updatedWorkspace;
    } catch (error) {
      console.error("Error updating workspace:", error);
      return null;
    }
  };

  const deleteWorkspace = async (id: string): Promise<boolean> => {
    try {
      // Find the organization in Clerk
      const membership = userMemberships?.data.find(m => m.organization.id === id);
      if (!membership) {
        return false;
      }

      // Delete the organization in Clerk
      await membership.organization.destroy();

      // Remove the workspace from the local state
      const updatedWorkspaces = userWorkspaces.filter(ws => ws.id !== id);
      setUserWorkspaces(updatedWorkspaces);

      // Remove the workspace ID from the current user's workspaces
      if (currentUser) {
        setCurrentUser({
          ...currentUser,
          workspaces: currentUser.workspaces.filter(wsId => wsId !== id)
        });
      }

      // If the current workspace is being deleted, set the current workspace to the first available workspace
      if (currentWorkspace && currentWorkspace.id === id) {
        if (updatedWorkspaces.length > 0) {
          setCurrentWorkspaceState(updatedWorkspaces[0]);
        } else {
          setCurrentWorkspaceState(null);
        }
      }

      return true;
    } catch (error) {
      console.error("Error deleting workspace:", error);
      return false;
    }
  };

  const getWorkspaceById = (id: string): Workspace | undefined => {
    return userWorkspaces.find(ws => ws.id === id);
  };

  // Get workspaces that the current user is a member of
  const getUserWorkspaces = (): Workspace[] => {
    return userWorkspaces;
  };

  // Check if the user can access a specific workspace
  const canAccessWorkspace = (workspaceId: string): boolean => {
    if (!currentUser || !workspaceId) return false;
    return currentUser.workspaces.includes(workspaceId);
  };

  // Check if the user can access content from a specific workspace
  const canAccessContent = (workspaceId: string | undefined): boolean => {
    if (!workspaceId || !currentUser) return false;
    return currentUser.workspaces.includes(workspaceId);
  };

  // Validate workspace access and throw error if denied
  const validateWorkspaceAccess = (workspaceId: string): void => {
    if (!workspaceId) {
      throw new Error('Workspace ID is required');
    }

    if (!currentUser) {
      throw new Error('User authentication required');
    }

    if (!canAccessWorkspace(workspaceId)) {
      throw new Error(`Access denied: You don't have permission to access workspace ${workspaceId}`);
    }
  };

  // Enhanced workspace switching with access validation
  const switchToWorkspace = async (workspaceId: string): Promise<void> => {
    try {
      // Validate access before switching
      validateWorkspaceAccess(workspaceId);

      // Find the workspace in user's accessible workspaces
      const targetWorkspace = userWorkspaces.find(w => w.id === workspaceId);
      if (!targetWorkspace) {
        throw new Error(`Workspace ${workspaceId} not found in your accessible workspaces`);
      }

      // Switch to the workspace
      await setCurrentWorkspace(targetWorkspace);

    } catch (error) {
      console.error('❌ Failed to switch workspace:', error);
      throw error;
    }
  };

  // Role management functions
  const getWorkspaceRoles = async (workspaceId: string): Promise<Role[]> => {
    try {
      // Find the organization in Clerk
      const membership = userMemberships?.data.find(m => m.organization.id === workspaceId);
      if (!membership) {
        return [];
      }

      // Get roles from organization metadata
      const metadata = membership.organization.publicMetadata || {};

      // If roles are not initialized, initialize them
      if (!metadata.rolesInitialized) {
        await initializeOrganizationRoles(membership.organization);
        // Refresh the metadata
        const updatedMembership = await membership.organization.reload();
        const updatedMetadata = updatedMembership.publicMetadata || {};
        return (updatedMetadata.roles as Role[]) || DEFAULT_ROLES;
      }

      return (metadata.roles as Role[]) || DEFAULT_ROLES;
    } catch (error) {
      console.error("Error getting workspace roles:", error);
      return DEFAULT_ROLES;
    }
  };

  const getWorkspacePermissions = async (workspaceId: string): Promise<Permission[]> => {
    try {
      // Find the organization in Clerk
      const membership = userMemberships?.data.find(m => m.organization.id === workspaceId);
      if (!membership) {
        return [];
      }

      // Get permissions from organization metadata
      const metadata = membership.organization.publicMetadata || {};

      // If roles are not initialized, initialize them
      if (!metadata.rolesInitialized) {
        await initializeOrganizationRoles(membership.organization);
        // Refresh the metadata
        const updatedMembership = await membership.organization.reload();
        const updatedMetadata = updatedMembership.publicMetadata || {};
        return (updatedMetadata.permissions as Permission[]) || DEFAULT_PERMISSIONS;
      }

      return (metadata.permissions as Permission[]) || DEFAULT_PERMISSIONS;
    } catch (error) {
      console.error("Error getting workspace permissions:", error);
      return DEFAULT_PERMISSIONS;
    }
  };

  // Simplified role management - only keep essential functions

  // User role management functions
  const getUserRole = async (workspaceId: string, userId: string): Promise<Role | null> => {
    try {
      // Find the organization in Clerk
      const membership = userMemberships?.data.find(m => m.organization.id === workspaceId);
      if (!membership) {
        return null;
      }

      // Get the organization
      const organization = membership.organization;

      // Get the user's membership in the organization
      const members = await organization.getMemberships();
      const userMembership = members.data.find((m: any) => m.publicUserData?.userId === userId);

      if (!userMembership) {
        return null;
      }

      // Get the user's role ID from the membership
      const roleId = getRoleIdFromMembership(userMembership);

      // Get all roles for the workspace
      const roles = await getWorkspaceRoles(workspaceId);

      // Find the role by ID
      const role = roles.find(r => r.id === roleId);

      return role || null;
    } catch (error) {
      console.error("Error getting user role:", error);
      return null;
    }
  };

  // Permission checking functions
  const hasPermission = async (workspaceId: string, userId: string, permissionId: string): Promise<boolean> => {
    try {
      // Get the user's role
      const role = await getUserRole(workspaceId, userId);

      if (!role) {
        return false;
      }

      // Check if the role has the permission
      return role.permissions.includes(permissionId);
    } catch (error) {
      console.error("Error checking permission:", error);
      return false;
    }
  };

  const hasAnyPermission = async (workspaceId: string, userId: string, permissionIds: string[]): Promise<boolean> => {
    try {
      // Get the user's role
      const role = await getUserRole(workspaceId, userId);

      if (!role) {
        return false;
      }

      // Check if the role has any of the permissions
      return permissionIds.some(permId => role.permissions.includes(permId));
    } catch (error) {
      console.error("Error checking permissions:", error);
      return false;
    }
  };

  return (
    <ClerkWorkspaceContext.Provider
      value={{
        currentWorkspace,
        setCurrentWorkspace,
        currentUser,
        userWorkspaces,
        isUserInWorkspace,
        isContentInWorkspace,
        createWorkspace,
        updateWorkspace,
        deleteWorkspace,
        getWorkspaceById,
        getUserWorkspaces,
        canAccessWorkspace,
        canAccessContent,
        validateWorkspaceAccess,
        switchToWorkspace,
        // Role management
        getWorkspaceRoles,
        getWorkspacePermissions,
        // User role management
        getUserRole,
        // Permission checking
        hasPermission,
        hasAnyPermission,
        // Loading state
        isLoading
      }}
    >
      {children}
    </ClerkWorkspaceContext.Provider>
  );
};

export const useClerkWorkspace = () => {
  const context = useContext(ClerkWorkspaceContext);
  if (context === undefined) {
    throw new Error("useClerkWorkspace must be used within a ClerkWorkspaceProvider");
  }
  return context;
};
