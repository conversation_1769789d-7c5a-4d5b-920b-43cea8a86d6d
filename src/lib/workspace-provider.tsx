/**
 * Legacy Workspace Provider - DEPRECATED
 *
 * This file has been refactored as part of the workspace consolidation.
 * All workspace functionality now uses the Clerk-based provider.
 *
 * This file only re-exports types for backward compatibility.
 * New code should import directly from @/types/workspace and use @/lib/clerk-workspace-provider
 */

// Re-export unified types for backward compatibility
export type { Workspace } from "../types/workspace";

// Legacy User interface - kept for backward compatibility
// New code should use Clerk's user object directly
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
  initials: string;
  workspaces: string[]; // Array of workspace IDs the user belongs to
  workspaceRoles?: Record<string, string>; // Map of workspace IDs to role IDs
}

/**
 * DEPRECATED: Legacy workspace provider implementation removed
 *
 * All workspace functionality has been migrated to use Clerk-based authentication
 * and the unified workspace service architecture.
 *
 * For new implementations, use:
 * - @/lib/clerk-workspace-provider for workspace context
 * - @/services/workspace-service for API operations
 * - @/types/workspace for type definitions
 *
 * This file is kept only for type re-exports to maintain backward compatibility.
 */

// If you need workspace functionality, import from the new providers:
// import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
// import { workspaceService } from "@/services/workspace-service";
