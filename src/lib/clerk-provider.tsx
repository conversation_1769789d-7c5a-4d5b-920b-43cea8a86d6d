import React from 'react';
import { Clerk<PERSON>rovider as BaseClerkProvider } from '@clerk/clerk-react';
import { dark } from '@clerk/themes';

interface ClerkProviderProps {
  children: React.ReactNode;
}

export const ClerkProvider: React.FC<ClerkProviderProps> = ({ children }) => {
  const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

  // Get theme from localStorage or default to light
  const getTheme = () => {
    try {
      const stored = localStorage.getItem('averum-theme');
      if (stored) return stored;

      // Check system preference
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return 'dark';
      }
      return 'light';
    } catch {
      return 'light';
    }
  };

  const currentTheme = getTheme();

  // Check if the key is missing
  if (!clerk<PERSON>ub<PERSON>ey) {
    // Return a more user-friendly error message
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
        <div className="max-w-md p-6 bg-background border border-border rounded-lg shadow-lg">
          <h1 className="text-2xl font-bold text-destructive mb-4">Clerk Configuration Error</h1>
          <p className="mb-4 text-foreground">
            You need to provide a valid Clerk publishable key in your <code className="px-1 py-0.5 bg-muted rounded text-sm">.env</code> file.
          </p>
          <ol className="text-left mb-6 space-y-2 text-muted-foreground">
            <li>1. Sign up for a Clerk account at <a href="https://clerk.com" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">clerk.com</a></li>
            <li>2. Create a new application in the Clerk dashboard</li>
            <li>3. Go to API Keys in your Clerk dashboard</li>
            <li>4. Copy your publishable key</li>
            <li>5. Update the <code className="px-1 py-0.5 bg-muted rounded text-sm">.env</code> file with your key</li>
          </ol>
          <div className="p-3 bg-muted rounded-md text-sm font-mono text-left overflow-x-auto">
            <pre>VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_actual_key</pre>
          </div>
        </div>
      </div>
    );
  }

  return (
    <BaseClerkProvider
      publishableKey={clerkPubKey}
      appearance={{
        baseTheme: currentTheme === 'dark' ? dark : undefined,
        elements: {
          formButtonPrimary:
            'bg-primary hover:bg-primary/90 text-primary-foreground',
          card: 'bg-background border border-border',
          formFieldInput: 'bg-background border border-input',
          footerActionLink: 'text-primary hover:text-primary/90',
          headerTitle: 'text-foreground',
          headerSubtitle: 'text-muted-foreground',
          formFieldLabel: 'text-foreground',
          formFieldHintText: 'text-muted-foreground',
          identityPreviewText: 'text-foreground',
          identityPreviewEditButton: 'text-primary hover:text-primary/90',
        },
      }}
    >
      {children}
    </BaseClerkProvider>
  );
};
