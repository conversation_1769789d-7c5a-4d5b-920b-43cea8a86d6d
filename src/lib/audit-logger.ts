import { useClerkWorkspace } from './clerk-workspace-provider';
import { useUser } from '@clerk/clerk-react';

// Define audit event types
export enum AuditEventType {
  // User events
  USER_INVITED = 'user.invited',
  USER_JOINED = 'user.joined',
  USER_REMOVED = 'user.removed',
  
  // Role events
  ROLE_CREATED = 'role.created',
  ROLE_UPDATED = 'role.updated',
  ROLE_DELETED = 'role.deleted',
  ROLE_ASSIGNED = 'role.assigned',
  ROLE_REVOKED = 'role.revoked',
  
  // Permission events
  PERMISSION_GRANTED = 'permission.granted',
  PERMISSION_REVOKED = 'permission.revoked',
  
  // Workspace events
  WORKSPACE_CREATED = 'workspace.created',
  WORKSPACE_UPDATED = 'workspace.updated',
  WORKSPACE_DELETED = 'workspace.deleted',
  
  // Access events
  ACCESS_GRANTED = 'access.granted',
  ACCESS_DENIED = 'access.denied',
  
  // Content events
  CONTENT_CREATED = 'content.created',
  CONTENT_UPDATED = 'content.updated',
  CONTENT_DELETED = 'content.deleted',
  CONTENT_VIEWED = 'content.viewed',
}

// Define audit log entry structure
export interface AuditLogEntry {
  id: string;
  timestamp: string;
  eventType: AuditEventType;
  userId: string;
  userName: string;
  workspaceId: string;
  workspaceName: string;
  targetId?: string;
  targetType?: string;
  targetName?: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Class for logging audit events
 */
export class AuditLogger {
  private static instance: AuditLogger;
  private logs: AuditLogEntry[] = [];
  private storageKey = 'averum_audit_logs';
  
  private constructor() {
    // Load logs from localStorage in browser environment
    if (typeof window !== 'undefined') {
      const storedLogs = localStorage.getItem(this.storageKey);
      if (storedLogs) {
        try {
          this.logs = JSON.parse(storedLogs);
        } catch (error) {
          console.error('Error parsing stored audit logs:', error);
          this.logs = [];
        }
      }
    }
  }
  
  /**
   * Get the singleton instance of AuditLogger
   */
  public static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }
  
  /**
   * Log an audit event
   * @param event The audit event to log
   */
  public log(event: Omit<AuditLogEntry, 'id' | 'timestamp'>): AuditLogEntry {
    const logEntry: AuditLogEntry = {
      id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      ...event,
    };
    
    this.logs.unshift(logEntry); // Add to beginning of array
    
    // Keep only the last 1000 logs to prevent localStorage from getting too large
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(0, 1000);
    }
    
    // Save to localStorage in browser environment
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.storageKey, JSON.stringify(this.logs));
    }
    
    // In a real implementation, you would also send this to a server
    console.log('Audit log:', logEntry);
    
    return logEntry;
  }
  
  /**
   * Get all audit logs
   */
  public getLogs(): AuditLogEntry[] {
    return [...this.logs];
  }
  
  /**
   * Get audit logs for a specific workspace
   * @param workspaceId The workspace ID to filter by
   */
  public getWorkspaceLogs(workspaceId: string): AuditLogEntry[] {
    return this.logs.filter(log => log.workspaceId === workspaceId);
  }
  
  /**
   * Get audit logs for a specific user
   * @param userId The user ID to filter by
   */
  public getUserLogs(userId: string): AuditLogEntry[] {
    return this.logs.filter(log => log.userId === userId);
  }
  
  /**
   * Get audit logs for a specific event type
   * @param eventType The event type to filter by
   */
  public getEventLogs(eventType: AuditEventType): AuditLogEntry[] {
    return this.logs.filter(log => log.eventType === eventType);
  }
  
  /**
   * Clear all audit logs
   */
  public clearLogs(): void {
    this.logs = [];
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.storageKey);
    }
  }
}

/**
 * Hook for using the audit logger
 */
export function useAuditLogger() {
  const { user } = useUser();
  const { currentWorkspace } = useClerkWorkspace();
  const logger = AuditLogger.getInstance();
  
  /**
   * Log an audit event with current user and workspace context
   * @param eventType The type of event to log
   * @param details Additional details about the event
   */
  const logEvent = (
    eventType: AuditEventType,
    options: {
      targetId?: string;
      targetType?: string;
      targetName?: string;
      details?: Record<string, any>;
      workspaceId?: string;
      workspaceName?: string;
    } = {}
  ) => {
    if (!user) {
      console.warn('Cannot log audit event: No user is logged in');
      return null;
    }
    
    const workspaceId = options.workspaceId || currentWorkspace?.id;
    const workspaceName = options.workspaceName || currentWorkspace?.name;
    
    if (!workspaceId || !workspaceName) {
      console.warn('Cannot log audit event: No workspace context');
      return null;
    }
    
    return logger.log({
      eventType,
      userId: user.id,
      userName: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username || 'Unknown User',
      workspaceId,
      workspaceName,
      ...options,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
    });
  };
  
  return {
    logEvent,
    getLogs: logger.getLogs.bind(logger),
    getWorkspaceLogs: logger.getWorkspaceLogs.bind(logger),
    getUserLogs: logger.getUserLogs.bind(logger),
    getEventLogs: logger.getEventLogs.bind(logger),
  };
}
