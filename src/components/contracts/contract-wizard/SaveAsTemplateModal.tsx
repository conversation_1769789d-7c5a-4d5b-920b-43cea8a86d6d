import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { useContractWizard, ContractData } from './ContractWizardContext';
import { useApi } from '@/lib/api';
import { TemplateService } from '@/services/api-services';
import { TemplateContent } from '@/services/api-types';
import { useClerkWorkspace } from '@/lib/clerk-workspace-provider';
import { Loader2, Save } from 'lucide-react';
import { industryOptions } from './contractTemplates';

interface SaveAsTemplateModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Helper function to convert ContractData to TemplateContent
const convertContractDataToTemplateContent = (data: ContractData): TemplateContent => {
  const sections = [];
  const variables = [];
  const metadata: Record<string, string | number | boolean> = {};

  // Add basic contract information as sections
  if (data.title) {
    sections.push({
      title: "Contract Title",
      content: data.title,
      required: true
    });
  }

  if (data.description) {
    sections.push({
      title: "Description",
      content: data.description,
      required: false
    });
  }

  if (data.scope) {
    sections.push({
      title: "Scope of Work",
      content: data.scope,
      required: true
    });
  }

  if (data.paymentTerms) {
    sections.push({
      title: "Payment Terms",
      content: data.paymentTerms,
      required: true
    });
  }

  // Add variables for dynamic fields
  variables.push(
    { name: "jurisdiction", type: "text" as const, required: true, default: data.jurisdiction },
    { name: "contractType", type: "text" as const, required: true, default: data.contractType },
    { name: "effectiveDate", type: "date" as const, required: true, default: data.effectiveDate },
    { name: "duration", type: "text" as const, required: true, default: data.duration },
    { name: "contractValue", type: "text" as const, required: false, default: data.contractValue },
    { name: "currency", type: "text" as const, required: false, default: data.currency },
    { name: "paymentMethod", type: "text" as const, required: false, default: data.paymentMethod }
  );

  // Add metadata
  metadata.jurisdiction = data.jurisdiction;
  metadata.contractType = data.contractType;
  metadata.currency = data.currency;
  metadata.approvalProcess = data.approvalProcess || 'sequential';
  if (data.industry) metadata.industry = data.industry;

  return {
    sections,
    variables,
    metadata
  };
};

const SaveAsTemplateModal: React.FC<SaveAsTemplateModalProps> = ({ open, onOpenChange }) => {
  const { data } = useContractWizard();
  const { toast } = useToast();
  const { fetch } = useApi();
  const { currentWorkspace } = useClerkWorkspace();

  const [templateName, setTemplateName] = useState(data.title || '');
  const [templateDescription, setTemplateDescription] = useState(data.description || '');
  const [templateType, setTemplateType] = useState(data.contractType || '');
  const [templateIndustry, setTemplateIndustry] = useState(data.industry || '');
  const [templateComplexity, setTemplateComplexity] = useState<'simple' | 'medium' | 'complex'>('medium');
  const [isPublic, setIsPublic] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Reset form when modal opens
  React.useEffect(() => {
    if (open) {
      setTemplateName(data.title || '');
      setTemplateDescription(data.description || '');
      setTemplateType(data.contractType || '');
      setTemplateIndustry(data.industry || '');
      setTemplateComplexity('medium');
      setIsPublic(false);
    }
  }, [open, data]);

  const handleSaveTemplate = async () => {
    if (!templateName.trim()) {
      toast({
        title: "Template name required",
        description: "Please enter a name for your template.",
        variant: "destructive"
      });
      return;
    }

    if (!currentWorkspace?.id) {
      toast({
        title: "No workspace selected",
        description: "Please select a workspace before saving a template.",
        variant: "destructive"
      });
      return;
    }

    setIsSaving(true);

    try {
      // Prepare template data
      const templateData = {
        title: templateName.trim(),
        description: templateDescription.trim(),
        type: templateType,
        complexity: templateComplexity,
        industry: templateIndustry,
        is_public: isPublic,
        workspace_id: currentWorkspace.id,
        content: convertContractDataToTemplateContent(data),
        tags: [templateType, templateIndustry].filter(Boolean)
      };

      // Save template
      const result = await fetch(
        () => TemplateService.createTemplate(templateData),
        "Saving template...",
        "Failed to save template"
      );

      if (result) {
        toast({
          title: "Template saved",
          description: "Your contract has been saved as a template.",
        });

        onOpenChange(false);
      }
    } catch (error) {
      console.error("Error saving template:", error);
      toast({
        title: "Error saving template",
        description: "An error occurred while saving the template. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Save as Template</DialogTitle>
          <DialogDescription>
            Create a reusable template from your current contract.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="templateName">Template Name</Label>
            <Input
              id="templateName"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
              placeholder="Enter template name"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="templateDescription">Description</Label>
            <Textarea
              id="templateDescription"
              value={templateDescription}
              onChange={(e) => setTemplateDescription(e.target.value)}
              placeholder="Enter template description"
              rows={3}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="templateType">Contract Type</Label>
            <Input
              id="templateType"
              value={templateType}
              onChange={(e) => setTemplateType(e.target.value)}
              placeholder="E.g., Service Agreement, NDA, etc."
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="templateIndustry">Industry</Label>
            <Select value={templateIndustry} onValueChange={setTemplateIndustry}>
              <SelectTrigger id="templateIndustry">
                <SelectValue placeholder="Select industry" />
              </SelectTrigger>
              <SelectContent>
                {industryOptions.map((industry) => (
                  <SelectItem key={industry} value={industry}>
                    {industry}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="templateComplexity">Complexity</Label>
            <Select value={templateComplexity} onValueChange={(value) => setTemplateComplexity(value as 'simple' | 'medium' | 'complex')}>
              <SelectTrigger id="templateComplexity">
                <SelectValue placeholder="Select complexity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="simple">Simple</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="complex">Complex</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isPublic"
              checked={isPublic}
              onCheckedChange={(checked) => setIsPublic(checked === true)}
            />
            <Label htmlFor="isPublic" className="text-sm font-normal">
              Make this template available to all workspace members
            </Label>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSaveTemplate} disabled={isSaving || !templateName.trim()}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Template
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SaveAsTemplateModal;
