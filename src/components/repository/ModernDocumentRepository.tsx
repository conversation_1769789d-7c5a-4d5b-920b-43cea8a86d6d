import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  FileText,
  MoreHorizontal,
  Copy,
  Loader2,

  Plus,
  LayoutGrid,
  LayoutList,
  Download,
  ArrowRight,
  Clock,
  Users,
  GraduationCap,
  Code,
  Home,
  Factory,
  ShoppingBag,
  Search,
  FolderPlus,
  Folder,
  Trash2,
  Edit,
  ChevronRight,
  ChevronDown,
  FolderOpen,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";


import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { Template, templateStore } from "@/types/template";
import { useDocumentPreview } from "@/engines/document-engine/hooks/useDocumentPreview";
import { DocumentPreviewModal } from "@/engines/document-engine/preview/DocumentPreviewModal";
import { useApi } from "@/lib/api";
import CompactSearch from "@/components/ui/compact-search";
import { ContractService, TemplateService, FolderService } from "@/services/api-services";


import type { RepositoryFolder } from '@/types/repository';

interface Contract {
  id: string;
  title: string;
  type: string;
  status: "draft" | "active" | "expired" | "terminated" | "pending_approval" | "rejected";
  createdBy: {
    name: string;
    id?: string;
  };
  createdDate: string;
  expiryDate?: string;
  counterparty: string;
  tags: string[];
  starred: boolean;
  workspaceId?: string;
  folderId?: string;
}

interface TemplateWithSource extends Template {
  source: 'averum' | 'custom' | 'shared';
  sharedBy?: string;
}

interface ModernDocumentRepositoryProps {
  onSelectTemplate?: (templateId: string) => void;
}

const ModernDocumentRepository = ({ onSelectTemplate }: ModernDocumentRepositoryProps = {}) => {
  // Get current workspace from context
  const { currentWorkspace, canAccessContent } = useClerkWorkspace();
  const { fetch, fetchArray } = useApi();

  // State for data
  const [, setTemplates] = useState<TemplateWithSource[]>([]);
  const [, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Folder state
  const [folders, setFolders] = useState<RepositoryFolder[]>([]);

  // UI state

  const [selectedFolder, setSelectedFolder] = useState<string>('all');
  const [isCreateFolderOpen, setIsCreateFolderOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [newFolderParentId, setNewFolderParentId] = useState<string | null>(null);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['averum-templates', 'shared-with-me', 'my-custom-folders']));
  const [isRenamingFolder, setIsRenamingFolder] = useState<string | null>(null);
  const [renameFolderValue, setRenameFolderValue] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filteredTemplates] = useState<TemplateWithSource[]>([]);

  // Document preview
  const { previewState, openPreview, closePreview } = useDocumentPreview();





  // Load data
  useEffect(() => {
    const fetchData = async () => {
      if (!canAccessContent) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // Load templates from localStorage first
        templateStore.loadFromLocalStorage();
        const localTemplates = templateStore.getTemplates();

        // Convert to TemplateWithSource format
        const templatesWithSource: TemplateWithSource[] = localTemplates.map(template => ({
          ...template,
          source: 'custom' as const
        }));

        setTemplates(templatesWithSource);

        // Fetch from API if workspace is available
        if (currentWorkspace?.id) {
          const params = { workspace_id: currentWorkspace.id };

          // Fetch templates
          const apiTemplates = await fetchArray(
            () => TemplateService.getTemplates(params),
            "Loading templates...",
            "Failed to load templates"
          );

          if (apiTemplates && apiTemplates.length > 0) {
            // Map API templates to UI templates with proper structure
            const apiTemplatesWithSource: TemplateWithSource[] = apiTemplates.map(template => ({
              id: template.id,
              title: template.title,
              description: template.description,
              type: template.type,
              complexity: template.complexity,
              industry: template.industry || '',
              tags: template.tags || [],
              icon: template.icon || '',
              lastUpdated: template.updated_at || template.created_at,
              usageCount: template.usage_count,
              isUserCreated: template.is_user_created,
              folderId: template.folder_id || 'folder-4', // Default to templates folder
              source: 'averum' as const
            }));

            // Merge with local templates
            const mergedTemplates = [...templatesWithSource];

            // Add API templates that don't exist locally
            apiTemplatesWithSource.forEach(apiTemplate => {
              if (!templatesWithSource.some(localTemplate => localTemplate.id === apiTemplate.id)) {
                mergedTemplates.push(apiTemplate);
              }
            });

            setTemplates(mergedTemplates);

            // Update local storage with merged templates
            templateStore.setTemplates(mergedTemplates);
            templateStore.saveToLocalStorage();
          }

          // Fetch contracts
          const apiContracts = await fetchArray(
            () => ContractService.getContracts(params),
            "Loading contracts...",
            "Failed to load contracts"
          );

          if (apiContracts) {
            const contractsData: Contract[] = apiContracts.map(contract => ({
              id: contract.id,
              title: contract.title,
              type: contract.type || 'Contract',
              status: contract.status as Contract['status'],
              createdBy: {
                name: typeof contract.created_by === 'string' ? contract.created_by : contract.created_by?.name || 'Unknown',
                id: typeof contract.created_by === 'object' ? contract.created_by?.id : undefined
              },
              createdDate: new Date(contract.created_at).toISOString().split('T')[0],
              expiryDate: contract.expiry_date ? new Date(contract.expiry_date).toISOString().split('T')[0] : undefined,
              counterparty: contract.counterparty || 'Unknown',
              tags: contract.tags || [],
              starred: false,
              workspaceId: contract.workspace_id,
              folderId: contract.folder_id
            }));

            setContracts(contractsData);
          }
        }
      } catch (err) {
        console.error("Error fetching repository data:", err);
        setError("Failed to load repository data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentWorkspace?.id, fetch, fetchArray, canAccessContent]);

  // Load folders from API and create hierarchical structure
  useEffect(() => {
    const loadFolders = async () => {
      if (!currentWorkspace?.id) return;
      try {
        await FolderService.getFolders(currentWorkspace.id);
        // TODO: integrate actual folders from response if needed
        const defaultFolders: RepositoryFolder[] = [
          {
            id: 'averum-templates',
            name: 'Averum Templates',
            type: 'system',
            parentId: null,
            documentCount: 0,
            icon: 'folder',
            workspaceId: currentWorkspace.id,
            createdBy: 'system',
            createdDate: new Date().toISOString(),
            isParent: true
          },
          {
            id: 'shared-with-me',
            name: 'Shared with Me',
            type: 'system',
            parentId: null,
            documentCount: 0,
            icon: 'folder',
            workspaceId: currentWorkspace.id,
            createdBy: 'system',
            createdDate: new Date().toISOString(),
            isParent: true
          },
          {
            id: 'my-custom-folders',
            name: 'My Custom Folders',
            type: 'system',
            parentId: null,
            documentCount: 0,
            icon: 'folder',
            workspaceId: currentWorkspace.id,
            createdBy: 'system',
            createdDate: new Date().toISOString(),
            isParent: true
          }
        ];
        setFolders(defaultFolders);
      } catch (error) {
        console.error('Error loading folders:', error);
        const defaultFolders: RepositoryFolder[] = [
          {
            id: 'averum-templates',
            name: 'Averum Templates',
            type: 'system',
            parentId: null,
            documentCount: 0,
            icon: 'folder',
            workspaceId: currentWorkspace?.id || '',
            createdBy: 'system',
            createdDate: new Date().toISOString(),
            isParent: true
          }
        ];
        setFolders(defaultFolders);
      }
    };

    loadFolders();
  }, [currentWorkspace?.id]);

// Folder management functions
const handleCreateFolder = async () => {
  if (!newFolderName.trim() || !currentWorkspace?.id) return;

  try {
    const response = await FolderService.createFolder({
      name: newFolderName.trim(),
      workspace_id: currentWorkspace.id,
      description: `Custom folder: ${newFolderName.trim()}`,
      parent_id: newFolderParentId || 'my-custom-folders' // Default to custom folders section
    });

    if (response.data) {
      // Add the new folder to the list with UI-specific properties
      const newFolder: RepositoryFolder = {
        id: response.data.id,
        name: response.data.name,
        parentId: response.data.parent_id || null,
        workspaceId: response.data.workspace_id,
        createdBy: response.data.created_by || '',
        createdDate: response.data.created_at || new Date().toISOString(),
        documentCount: 0,
        type: 'custom',
        icon: 'folder',
        isParent: false
      };

      setFolders(prev => [...prev, newFolder]);
      setNewFolderName('');
      setNewFolderParentId(null);
      setIsCreateFolderOpen(false);

      console.log('Created folder:', newFolder);
    }
  } catch (error) {
    console.error('Error creating folder:', error);
    // TODO: Show error toast to user
  }
};

const handleDeleteFolder = async (folderId: string) => {
  try {
    await FolderService.deleteFolder(folderId);

    // Remove from local state
    setFolders(prev => prev.filter(folder => folder.id !== folderId));

    // Reset selected folder if it was deleted
    if (selectedFolder === folderId) {
      setSelectedFolder('all');
    }

    console.log('Deleted folder:', folderId);
  } catch (error) {
    console.error('Error deleting folder:', error);
    // TODO: Show error toast to user
  }
};



const toggleFolderExpansion = (folderId: string) => {
  setExpandedFolders(prev => {
    const newSet = new Set(prev);
    if (newSet.has(folderId)) {
      newSet.delete(folderId);
    } else {
      newSet.add(folderId);
    }
    return newSet;
  });
};

const handleRenameFolder = async (folderId: string, newName: string) => {
  if (!newName.trim()) return;

  try {
    await FolderService.updateFolder(folderId, { name: newName.trim() });

    // Update local state
    setFolders(prev => prev.map(folder =>
      folder.id === folderId
        ? { ...folder, name: newName.trim() }
        : folder
    ));

    setIsRenamingFolder(null);
    setRenameFolderValue('');
  } catch (error) {
    console.error('Error renaming folder:', error);
    // TODO: Show error toast to user
  }
};

// Template and contract handlers
const handleUseTemplate = (templateId: string) => {
  if (onSelectTemplate) {
    onSelectTemplate(templateId);
  }
};





// Render hierarchical folder structure
const renderFolderTree = (parentId: string | null = null, level: number = 0) => {
  const childFolders = folders.filter(folder => folder.parentId === parentId);

    return childFolders.map((folder) => {
      const isExpanded = expandedFolders.has(folder.id);
      const hasChildren = folders.some(f => f.parentId === folder.id);
      const isRenaming = isRenamingFolder === folder.id;

      return (
        <div key={folder.id}>
          <div className="flex items-center group" style={{ paddingLeft: `${level * 16}px` }}>
            {/* Expand/Collapse button for parent folders */}
            {(hasChildren || folder.isParent) && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 mr-1"
                onClick={() => toggleFolderExpansion(folder.id)}
              >
                {isExpanded ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
              </Button>
            )}

            {/* Folder button */}
            <Button
              variant={selectedFolder === folder.id ? 'secondary' : 'ghost'}
              size="sm"
              className="justify-start h-8 px-2 text-sm flex-1"
              onClick={() => setSelectedFolder(folder.id)}
              style={{ marginLeft: hasChildren || folder.isParent ? '0' : '28px' }}
            >
              {isExpanded && (hasChildren || folder.isParent) ? (
                <FolderOpen className="h-3 w-3 mr-2" />
              ) : (
                <Folder className="h-3 w-3 mr-2" />
              )}
              {isRenaming ? (
                <Input
                  value={renameFolderValue}
                  onChange={(e) => setRenameFolderValue(e.target.value)}
                  onBlur={() => handleRenameFolder(folder.id, renameFolderValue)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleRenameFolder(folder.id, renameFolderValue);
                    } else if (e.key === 'Escape') {
                      setIsRenamingFolder(null);
                      setRenameFolderValue('');
                    }
                  }}
                  className="h-6 text-xs"
                  autoFocus
                />
              ) : (
                folder.name
              )}
            </Button>

            {/* Action buttons for custom folders */}
            {folder.type === 'custom' && !isRenaming && (
              <div className="opacity-0 group-hover:opacity-100 transition-opacity flex">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 mr-1"
                  onClick={() => {
                    setIsRenamingFolder(folder.id);
                    setRenameFolderValue(folder.name);
                  }}
                >
                  <Edit className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={() => handleDeleteFolder(folder.id)}
                >
                  <Trash2 className="h-3 w-3 text-destructive" />
                </Button>
              </div>
            )}

            {/* Add subfolder button for parent folders */}
            {folder.isParent && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => {
                  setNewFolderParentId(folder.id);
                  setIsCreateFolderOpen(true);
                }}
              >
                <FolderPlus className="h-3 w-3" />
              </Button>
            )}
          </div>

          {/* Render children if expanded */}
          {isExpanded && renderFolderTree(folder.id, level + 1)}
        </div>
      );
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };



  // Get template icon (matching Templates page)
  const getTemplateIcon = (template: TemplateWithSource) => {
    switch (template.icon) {
      case "service":
        return <Users className="h-5 w-5 text-blue-600" />;
      case "nda":
        return <FileText className="h-5 w-5 text-purple-600" />;
      case "employment":
        return <GraduationCap className="h-5 w-5 text-green-600" />;
      case "software":
        return <Code className="h-5 w-5 text-cyan-600" />;
      case "lease":
        return <Home className="h-5 w-5 text-amber-600" />;
      case "manufacturing":
        return <Factory className="h-5 w-5 text-orange-600" />;
      case "sales":
        return <ShoppingBag className="h-5 w-5 text-pink-600" />;
      case "blank":
        return <FileText className="h-5 w-5 text-muted-foreground" />;
      default:
        return <FileText className="h-5 w-5 text-primary" />;
    }
  };



  // Template Card Component (exact match to Templates page)
  const TemplateCard = ({ template }: { template: TemplateWithSource }) => (
    <Card className="overflow-hidden border hover:border-primary/50 transition-all">
      <CardHeader className="pb-2 pt-4">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-sm font-medium">{template.title}</CardTitle>
            <CardDescription className="line-clamp-2 text-xs mt-1">
              {template.description}
            </CardDescription>
          </div>
          <div className="flex items-center">
            {getTemplateIcon(template)}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pb-2">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            {formatDate(template.lastUpdated)}
          </div>
          <span>Used {template.usageCount} times</span>
        </div>
      </CardContent>

      <CardFooter className="pt-0 pb-4 flex justify-end">
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="outline"
            className="h-8 text-xs"
            onClick={() => handleUseTemplate(template.id)}
          >
            Use Template
            <ArrowRight className="ml-1 h-3 w-3" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => openPreview(template.description || '', `${template.title} - Preview`)}>
                Preview Template
              </DropdownMenuItem>
              <DropdownMenuItem>View Details</DropdownMenuItem>
              <DropdownMenuItem>Duplicate</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardFooter>
    </Card>
  );

  // Template List View Component
  const TemplateListView = ({ templates }: { templates: TemplateWithSource[] }) => (
    <Card className="border border-gray-200 shadow-none">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Template</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Industry</TableHead>
            <TableHead>Complexity</TableHead>
            <TableHead>Usage</TableHead>
            <TableHead>Updated</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {templates.map((template) => (
            <TableRow key={template.id}>
              <TableCell>
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                    {getTemplateIcon(template)}
                  </div>
                  <div>
                    <div className="font-medium">{template.title}</div>
                    <div className="text-xs text-muted-foreground line-clamp-1">
                      {template.description}
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell className="text-sm">{template.type}</TableCell>
              <TableCell className="text-sm">{template.industry || 'General'}</TableCell>
              <TableCell>
                <Badge variant="outline" className="text-xs">
                  {template.complexity?.charAt(0).toUpperCase() + template.complexity?.slice(1)}
                </Badge>
              </TableCell>
              <TableCell className="text-sm">{template.usageCount} times</TableCell>
              <TableCell className="text-sm text-muted-foreground">
                {formatDate(template.lastUpdated)}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end gap-1">
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-8 text-xs"
                    onClick={() => handleUseTemplate(template.id)}
                  >
                    Use Template
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => openPreview(template.description || '', `${template.title} - Preview`)}>
                        Preview Template
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleUseTemplate(template.id)}>
                        Use Template
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Copy className="mr-2 h-4 w-4" />
                        Duplicate
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Download className="mr-2 h-4 w-4" />
                        Export
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Card>
  );



  // Contract components removed - not currently used in this repository view



  return (
    <div className="space-y-6">
      {/* Header - simplified without redundant search */}
      <div className="border-b pb-3">
        <div>
          <h2 className="text-base font-medium">Document Repository</h2>
          <p className="text-xs text-muted-foreground mt-1">
            Manage your templates, contracts, and documents
          </p>
        </div>
      </div>

      {loading ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
          <h3 className="text-lg font-medium">Loading repository...</h3>
          <p className="text-muted-foreground mt-2">
            Please wait while we fetch your documents
          </p>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FileText className="h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium">Error loading repository</h3>
          <p className="text-muted-foreground mt-2">{error}</p>
        </div>
      ) : (
        <div className="w-full">
          <div className="flex items-center justify-between mb-4">
            {/* Right side: View toggle, Search and primary action */}
            <div className="flex items-center gap-2">
              {/* View Mode Toggle */}
              <div className="flex border rounded-md">
                <Button
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="sm"
                  className="rounded-r-none h-8 px-3"
                  onClick={() => setViewMode("grid")}
                >
                  <LayoutGrid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="sm"
                  className="rounded-l-none h-8 px-3"
                  onClick={() => setViewMode("list")}
                >
                  <LayoutList className="h-4 w-4" />
                </Button>
              </div>

              <CompactSearch
                searchValue={{ global: searchQuery }}
                onSearchChange={(query) => setSearchQuery(query.global || '')}
                searchPlaceholder="Search repository..."
                recentSearches={[]}
                suggestions={[]}
                totalResults={filteredTemplates.length}
                showResultsCount={true}
              />
              <Button size="sm">
                <Plus className="mr-1.5 h-4 w-4" />
                New Template
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {/* Left Column - Filters */}
              <div className="space-y-6">
                <Card className="border border-gray-200 shadow-none">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg font-medium">Document Navigation</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium flex items-center">
                          <Folder className="h-4 w-4 mr-2" /> Folders
                        </h3>
                        <Dialog open={isCreateFolderOpen} onOpenChange={setIsCreateFolderOpen}>
                          <DialogTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                              <FolderPlus className="h-3 w-3" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-md">
                            <DialogHeader>
                              <DialogTitle>Create New Folder</DialogTitle>
                              <DialogDescription>
                                {newFolderParentId
                                  ? `Create a subfolder in "${folders.find(f => f.id === newFolderParentId)?.name}"`
                                  : "Create a custom folder to organize your documents"
                                }
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="space-y-2">
                                <Label htmlFor="folder-name">Folder Name</Label>
                                <Input
                                  id="folder-name"
                                  value={newFolderName}
                                  onChange={(e) => setNewFolderName(e.target.value)}
                                  placeholder="Enter folder name"
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                      handleCreateFolder();
                                    }
                                  }}
                                />
                              </div>
                            </div>
                            <DialogFooter>
                              <Button variant="outline" onClick={() => {
                                setIsCreateFolderOpen(false);
                                setNewFolderParentId(null);
                              }}>
                                Cancel
                              </Button>
                              <Button onClick={handleCreateFolder} disabled={!newFolderName.trim()}>
                                Create Folder
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>
                      <div className="space-y-1">
                        <Button
                          variant={selectedFolder === 'all' ? 'secondary' : 'ghost'}
                          size="sm"
                          className="justify-start h-8 px-2 text-sm w-full"
                          onClick={() => setSelectedFolder('all')}
                        >
                          All Documents
                        </Button>
                        {renderFolderTree()}
                      </div>
                    </div>








                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Content */}
              <div className="md:col-span-3">
                {filteredTemplates.length === 0 ? (
                  <div className="text-center py-8 bg-muted/30 rounded-lg">
                    <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-3">
                      <Search className="h-6 w-6 text-muted-foreground/60" />
                    </div>
                    <h3 className="text-sm font-medium mb-1.5">No templates found</h3>
                    <p className="text-sm text-muted-foreground max-w-md mx-auto">
                      We couldn&apos;t find any templates matching your search criteria. Try adjusting your search or browse all templates.
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-3 h-9 text-sm"
                      onClick={() => {
                        setSearchQuery("");
                        setSelectedFolder("all");
                      }}
                    >
                      Clear Filters
                    </Button>
                  </div>
                ) : (
                  viewMode === 'grid' ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {filteredTemplates.map((template) => (
                        <TemplateCard key={template.id} template={template} />
                      ))}
                    </div>
                  ) : (
                    <TemplateListView templates={filteredTemplates} />
                  )
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Document Preview Modal */}
      <DocumentPreviewModal
        isOpen={previewState.isOpen}
        onClose={closePreview}
        content={previewState.content}
        title={previewState.title}
        size="xl"
        previewProps={{
          showZoomControls: true,
          showPrintButton: true,
          showDownloadButton: true,
          showFullscreenButton: true
        }}
      />
    </div>
  );
};

export default ModernDocumentRepository;
