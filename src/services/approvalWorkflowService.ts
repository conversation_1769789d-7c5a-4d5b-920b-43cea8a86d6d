/**
 * Approval Workflow Service
 * 
 * Handles all API interactions for approval workflows including:
 * - Workflow creation and management
 * - Approval actions (approve/reject)
 * - AI-powered routing recommendations
 * - Bulk operations
 * - Template management
 */

import { api as apiClient } from '../lib/api-client';

export interface WorkflowType {
  SEQUENTIAL: 'sequential';
  PARALLEL: 'parallel';
  CONDITIONAL: 'conditional';
  HYBRID: 'hybrid';
}

export interface ApprovalStatus {
  PENDING: 'pending';
  APPROVED: 'approved';
  REJECTED: 'rejected';
  ESCALATED: 'escalated';
  EXPIRED: 'expired';
  SKIPPED: 'skipped';
}

export interface WorkflowStatus {
  DRAFT: 'draft';
  ACTIVE: 'active';
  COMPLETED: 'completed';
  REJECTED: 'rejected';
  CANCELLED: 'cancelled';
  ESCALATED: 'escalated';
}

export interface Priority {
  LOW: 'low';
  MEDIUM: 'medium';
  HIGH: 'high';
  URGENT: 'urgent';
}

export interface ApprovalCondition {
  field: string;
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains';
  value: string | number | boolean;
}

export interface Approver {
  id?: string;
  user_id: string;
  order: number;
  required: boolean;
  role: string;
  conditions?: Record<string, ApprovalCondition>;
  status?: string;
  created_at?: string;
  approved_at?: string;
  rejected_at?: string;
  comments?: string;
  user?: {
    name: string;
    email: string;
    avatar?: string;
  };
}

export interface EscalationRule {
  trigger: 'timeout' | 'rejection' | 'manual';
  timeout_hours?: number;
  escalate_to: string;
  notify_users?: string[];
}

export interface WorkflowProgress {
  total_approvals: number;
  completed_approvals: number;
  pending_approvals: number;
  progress_percentage: number;
  current_approver?: Approver;
  estimated_completion?: string;
}

export interface ApprovalWorkflow {
  id?: string;
  contract_id: string;
  workspace_id: string;
  workflow_type: keyof WorkflowType;
  status?: keyof WorkflowStatus;
  priority?: keyof Priority;
  created_by?: string;
  created_at?: string;
  started_at?: string;
  completed_at?: string;
  due_date?: string;
  escalation_rules?: Record<string, EscalationRule>;
  conditions?: Record<string, ApprovalCondition>;
  template_id?: string;
  rejection_reason?: string;
  approvers: Approver[];
  contract?: {
    title: string;
    type: string;
    value?: string;
  };
  progress?: WorkflowProgress;
}

export interface WorkflowSummary {
  total_workflows: number;
  active_workflows: number;
  completed_workflows: number;
  rejected_workflows: number;
  overdue_workflows: number;
  average_completion_time_hours: number;
}

export interface AIRoutingRecommendation {
  recommended_approvers: string[];
  workflow_type: keyof WorkflowType;
  priority: keyof Priority;
  estimated_time_hours: number;
  confidence: number;
  reasoning: string;
  risk_factors: string[];
}

export interface WorkflowFilters {
  workspace_id: string;
  status?: keyof WorkflowStatus;
  workflow_type?: keyof WorkflowType;
  priority?: keyof Priority;
  created_by?: string;
  contract_id?: string;
  search?: string;
  skip?: number;
  limit?: number;
}

export interface ApprovalAction {
  action: 'approve' | 'reject' | 'request_changes';
  comments?: string;
  conditions?: Record<string, ApprovalCondition>;
}

export interface BulkApprovalAction {
  approval_ids: string[];
  action: 'approve' | 'reject';
  comments?: string;
}

export interface BulkApprovalResult {
  success_count: number;
  failed_count: number;
  results: Array<{
    approval_id: string;
    success: boolean;
    error?: string;
  }>;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  workflow_type: keyof WorkflowType;
  approvers: Array<{
    role: string;
    order: number;
    required: boolean;
  }>;
  workspace_id?: string;
  created_at?: string;
  updated_at?: string;
}

class ApprovalWorkflowService {
  private baseUrl = '/approval-workflows';

  /**
   * Create a new approval workflow
   */
  async createWorkflow(workflowData: Omit<ApprovalWorkflow, 'id'>): Promise<ApprovalWorkflow> {
    try {
      const response = await apiClient.post<ApprovalWorkflow>(this.baseUrl, workflowData);
      return response.data;
    } catch (error) {
      console.error('Failed to create approval workflow:', error);
      throw new Error('Failed to create approval workflow');
    }
  }

  /**
   * Get approval workflows with filtering
   */
  async getWorkflows(filters: WorkflowFilters): Promise<ApprovalWorkflow[]> {
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await apiClient.get<ApprovalWorkflow[]>(`${this.baseUrl}?${params}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get approval workflows:', error);
      throw new Error('Failed to get approval workflows');
    }
  }

  /**
   * Get a specific approval workflow by ID
   */
  async getWorkflow(workflowId: string): Promise<ApprovalWorkflow> {
    try {
      const response = await apiClient.get<ApprovalWorkflow>(`${this.baseUrl}/${workflowId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get approval workflow:', error);
      throw new Error('Failed to get approval workflow');
    }
  }

  /**
   * Start an approval workflow
   */
  async startWorkflow(workflowId: string): Promise<ApprovalWorkflow> {
    try {
      const response = await apiClient.post<ApprovalWorkflow>(`${this.baseUrl}/${workflowId}/start`, {});
      return response.data;
    } catch (error) {
      console.error('Failed to start approval workflow:', error);
      throw new Error('Failed to start approval workflow');
    }
  }

  /**
   * Get workflow progress
   */
  async getWorkflowProgress(workflowId: string): Promise<WorkflowProgress> {
    try {
      const response = await apiClient.get<WorkflowProgress>(`${this.baseUrl}/${workflowId}/progress`);
      return response.data;
    } catch (error) {
      console.error('Failed to get workflow progress:', error);
      throw new Error('Failed to get workflow progress');
    }
  }

  /**
   * Approve a contract in the workflow
   */
  async approveContract(approvalId: string, action: ApprovalAction): Promise<Approver> {
    try {
      const response = await apiClient.post<Approver>(`${this.baseUrl}/approvals/${approvalId}/approve`, action);
      return response.data;
    } catch (error) {
      console.error('Failed to approve contract:', error);
      throw new Error('Failed to approve contract');
    }
  }

  /**
   * Reject a contract in the workflow
   */
  async rejectContract(approvalId: string, action: ApprovalAction): Promise<Approver> {
    try {
      if (!action.comments) {
        throw new Error('Rejection reason is required');
      }
      const response = await apiClient.post<Approver>(`${this.baseUrl}/approvals/${approvalId}/reject`, action);
      return response.data;
    } catch (error) {
      console.error('Failed to reject contract:', error);
      throw new Error('Failed to reject contract');
    }
  }

  /**
   * Get approvals assigned to the current user
   */
  async getMyApprovals(workspaceId: string, status?: keyof ApprovalStatus): Promise<Approver[]> {
    try {
      const params = new URLSearchParams({ workspace_id: workspaceId });
      if (status) {
        params.append('status', status);
      }

      const response = await apiClient.get<Approver[]>(`${this.baseUrl}/approvals/my-approvals?${params}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get user approvals:', error);
      throw new Error('Failed to get user approvals');
    }
  }

  /**
   * Get workflow summary statistics
   */
  async getWorkflowSummary(workspaceId: string): Promise<WorkflowSummary> {
    try {
      const response = await apiClient.get<WorkflowSummary>(`${this.baseUrl}/summary?workspace_id=${workspaceId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get workflow summary:', error);
      throw new Error('Failed to get workflow summary');
    }
  }

  /**
   * Get AI-powered routing recommendations
   */
  async getAIRoutingRecommendation(contractId: string): Promise<AIRoutingRecommendation> {
    try {
      const response = await apiClient.post<AIRoutingRecommendation>(`${this.baseUrl}/ai-routing/${contractId}`, {});
      return response.data;
    } catch (error) {
      console.error('Failed to get AI routing recommendation:', error);
      // Return fallback recommendation
      return {
        recommended_approvers: ['legal'],
        workflow_type: 'SEQUENTIAL',
        priority: 'MEDIUM',
        estimated_time_hours: 48,
        confidence: 0.5,
        reasoning: 'Fallback recommendation due to AI service unavailability',
        risk_factors: []
      };
    }
  }

  /**
   * Perform bulk approval actions
   */
  async bulkApprovalAction(bulkAction: BulkApprovalAction): Promise<BulkApprovalResult> {
    try {
      const response = await apiClient.post<BulkApprovalResult>(`${this.baseUrl}/bulk-approve`, bulkAction);
      return response.data;
    } catch (error) {
      console.error('Failed to perform bulk approval action:', error);
      throw new Error('Failed to perform bulk approval action');
    }
  }

  /**
   * Create a workflow from a contract with AI recommendations
   */
  async createWorkflowWithAI(contractId: string, workspaceId: string): Promise<ApprovalWorkflow> {
    try {
      // Get AI recommendations first
      const aiRecommendation = await this.getAIRoutingRecommendation(contractId);
      
      // Create workflow based on AI recommendations
      const workflowData: Omit<ApprovalWorkflow, 'id'> = {
        contract_id: contractId,
        workspace_id: workspaceId,
        workflow_type: aiRecommendation.workflow_type,
        priority: aiRecommendation.priority,
        due_date: new Date(Date.now() + aiRecommendation.estimated_time_hours * 60 * 60 * 1000).toISOString(),
        approvers: aiRecommendation.recommended_approvers.map((role, index) => ({
          user_id: '', // Would need to be resolved from role to actual user
          order: index + 1,
          required: true,
          role: role
        }))
      };

      return await this.createWorkflow(workflowData);
    } catch (error) {
      console.error('Failed to create AI-powered workflow:', error);
      throw new Error('Failed to create AI-powered workflow');
    }
  }

  /**
   * Get workflow templates for a workspace
   */
  async getWorkflowTemplates(workspaceId: string): Promise<WorkflowTemplate[]> {
    try {
      // This would be implemented when we add template endpoints
      const response = await apiClient.get<WorkflowTemplate[]>(`/workflow-templates?workspace_id=${workspaceId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get workflow templates:', error);
      // Return default templates as fallback (ensure we return an array)
      return [
        {
          id: 'default-sequential',
          name: 'Standard Sequential Approval',
          description: 'Standard approval workflow requiring sequential approvals',
          workflow_type: 'SEQUENTIAL',
          approvers: [
            { role: 'legal_reviewer', order: 1, required: true },
            { role: 'finance_approver', order: 2, required: true }
          ]
        },
        {
          id: 'default-parallel',
          name: 'Parallel Review',
          description: 'Parallel approval workflow for faster processing',
          workflow_type: 'PARALLEL',
          approvers: [
            { role: 'legal_reviewer', order: 1, required: true },
            { role: 'finance_approver', order: 1, required: true }
          ]
        }
      ];
    }
  }

  /**
   * Cancel a workflow
   */
  async cancelWorkflow(workflowId: string, reason?: string): Promise<ApprovalWorkflow> {
    try {
      const response = await apiClient.post<ApprovalWorkflow>(`${this.baseUrl}/${workflowId}/cancel`, {
        reason
      });
      return response.data;
    } catch (error) {
      console.error('Failed to cancel workflow:', error);
      throw new Error('Failed to cancel workflow');
    }
  }

  /**
   * Escalate a workflow
   */
  async escalateWorkflow(workflowId: string, escalateTo: string, reason: string): Promise<ApprovalWorkflow> {
    try {
      const response = await apiClient.post<ApprovalWorkflow>(`${this.baseUrl}/${workflowId}/escalate`, {
        escalate_to: escalateTo,
        reason
      });
      return response.data;
    } catch (error) {
      console.error('Failed to escalate workflow:', error);
      throw new Error('Failed to escalate workflow');
    }
  }
}

// Export singleton instance
export const approvalWorkflowService = new ApprovalWorkflowService();
export default approvalWorkflowService;
